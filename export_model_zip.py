import os
import subprocess

# === PARAMÈTRES MODIFIABLES ===
DATASET_ID = "030"
CONFIGURATION = "2d"
FOLD = "all"  # "all" ou un chiffre (0 à 4)
TRAINER_NAME = "nnUNetTrainer_750epochs"
PLANS_NAME = "nnUNetPlans"
OUTPUT_ZIP_NAME = f"model_{DATASET_ID}_{CONFIGURATION}.zip"

# === PATHS ===
RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
RESULTS_PATH = "/mnt/results/nnUnet_results"

# === ENV VARS ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH



# === CONSTRUCTION DE LA COMMANDE ===
cmd = (
    f"nnUNetv2_export_model_to_zip "
    f"-d {DATASET_ID} "
    f"-c {CONFIGURATION} "
    f"-tr {TRAINER_NAME} "
    f"-p {PLANS_NAME} "
    f"-f {FOLD} "
    f"-o {OUTPUT_ZIP_NAME}"
)

# === AFFICHAGE ET EXÉCUTION ===
print("🟢 Commande générée :")
print(cmd)
print("\n⏳ Exportation en cours...\n")

try:
    subprocess.run(cmd, shell=True, check=True)
    print(f"\n✅ Exportation réussie : {OUTPUT_ZIP_NAME}")
except subprocess.CalledProcessError as e:
    print(f"\n❌ Échec de l'exportation.\nErreur : {e}")
