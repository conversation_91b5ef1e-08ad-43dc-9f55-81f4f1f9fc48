import os
import subprocess
import time
import logging
from pathlib import Path

# === CONFIGURATION (MODIFIEZ ICI - MÊME CONFIG QUE train_nnunet.py) ===
DATASET_ID = "029"
CONFIGURATION = "2d"      # '2d', '3d_fullres', '3d_lowres'
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5                # Doit correspondre au modèle entraîné
FOLD = "all"              # "all" pour ensemble, ou "0", "1", "2", "3", "4" pour un fold spécifique

# === CHEMINS (MÊME CONFIG QUE train_nnunet.py) ===
RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
RESULTS_PATH = "/mnt/results/nnUnet_results"

# === CONFIGURATION ENVIRONNEMENT ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH

# === LOGGING SIMPLE ===
def setup_logging():
    """Configure le logging simple"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"nnunet_export_{DATASET_ID}_{timestamp}.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

def run_command(cmd: str, description: str = "") -> bool:
    """Exécute une commande avec gestion d'erreurs"""
    logger.info(f"🟢 {description if description else 'Lancement'}: {cmd}")

    try:
        subprocess.run(cmd, shell=True, check=True)
        logger.info(f"✅ Succès: {description}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Erreur lors de: {description}")
        logger.error(f"Code de retour: {e.returncode}")
        return False

def find_inference_files():
    """Trouve les fichiers d'inférence générés par train_nnunet.py"""
    import glob
    dataset_folder = os.path.join(RESULTS_PATH, f"Dataset{DATASET_ID}_*")
    folders = glob.glob(dataset_folder)

    if not folders:
        logger.error(f"❌ Dossier dataset non trouvé: {dataset_folder}")
        return None, None

    dataset_path = folders[0]
    inference_instructions = os.path.join(dataset_path, "inference_instructions.txt")
    inference_information = os.path.join(dataset_path, "inference_information.json")

    return inference_instructions, inference_information

def get_best_configuration():
    """Lit la meilleure configuration depuis inference_information.json"""
    _, inference_information = find_inference_files()

    if inference_information and os.path.exists(inference_information):
        try:
            import json
            with open(inference_information, 'r') as f:
                info = json.load(f)

            # Extraire la meilleure configuration
            if 'best_configuration' in info:
                best_config = info['best_configuration']
                logger.info(f"✅ Meilleure configuration trouvée: {best_config}")
                return best_config

        except Exception as e:
            logger.warning(f"⚠️  Erreur lecture inference_information.json: {e}")

    return None

def check_model_exists() -> bool:
    """Vérifie que le modèle entraîné existe et lit les informations d'inférence"""
    inference_instructions, inference_information = find_inference_files()

    # Vérifier les fichiers d'inférence
    if inference_instructions and os.path.exists(inference_instructions):
        logger.info(f"✅ Fichier d'instructions trouvé: {inference_instructions}")

    if inference_information and os.path.exists(inference_information):
        logger.info(f"✅ Fichier d'informations trouvé: {inference_information}")

        # Lire les informations de performance
        try:
            import json
            with open(inference_information, 'r') as f:
                info = json.load(f)

            if 'foreground_mean' in info:
                logger.info(f"📊 Performance moyenne: {info['foreground_mean']}")

        except Exception as e:
            logger.warning(f"⚠️  Erreur lecture informations: {e}")

    # Vérification du modèle
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
    model_folder = os.path.join(RESULTS_PATH, f"Dataset{DATASET_ID}_*",
                               f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}")
    import glob
    folders = glob.glob(model_folder)

    if not folders:
        logger.error(f"❌ Modèle non trouvé: {model_folder}")
        logger.error("💡 Assurez-vous d'avoir entraîné le modèle avec train_nnunet.py")
        return False

    logger.info(f"✅ Modèle trouvé: {folders[0]}")

    # Vérifier les folds disponibles
    if FOLD == "all":
        fold_folders = [f for f in os.listdir(folders[0]) if f.startswith("fold_")]
        logger.info(f"📁 Folds disponibles: {fold_folders}")
    else:
        fold_folder = os.path.join(folders[0], f"fold_{FOLD}")
        if not os.path.exists(fold_folder):
            logger.error(f"❌ Fold {FOLD} non trouvé: {fold_folder}")
            return False
        logger.info(f"✅ Fold {FOLD} trouvé")

    return True

def copy_inference_files_to_zip_folder():
    """Copie les fichiers d'inférence dans le dossier temporaire avant création du ZIP"""
    inference_instructions, inference_information = find_inference_files()

    if inference_instructions and os.path.exists(inference_instructions):
        logger.info("📋 Ajout des fichiers d'inférence au modèle exporté")

        # Les fichiers seront inclus automatiquement par nnUNetv2_export_model_to_zip
        # car ils sont dans le dossier du dataset
        return True

    logger.warning("⚠️  Fichiers d'inférence non trouvés, export du modèle seulement")
    return True

def export_model() -> bool:
    """Exporte le modèle en fichier ZIP avec les fichiers d'inférence"""
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"

    # Obtenir la meilleure configuration si disponible
    best_config = get_best_configuration()
    if best_config:
        config_suffix = "_optimized"
    else:
        config_suffix = ""

    output_zip_name = f"model_{DATASET_ID}_{CONFIGURATION}_{trainer_class}_fold{FOLD}{config_suffix}.zip"

    # Vérifier et préparer les fichiers d'inférence
    copy_inference_files_to_zip_folder()

    # Construction de la commande
    cmd = (
        f"nnUNetv2_export_model_to_zip "
        f"-d {DATASET_ID} "
        f"-c {CONFIGURATION} "
        f"-tr {trainer_class} "
        f"-p {PLANS_NAME} "
        f"-f {FOLD} "
        f"-o {output_zip_name}"
    )

    success = run_command(cmd, f"Exportation du modèle vers {output_zip_name}")

    if success:
        # Vérifier que le fichier ZIP a été créé
        if os.path.exists(output_zip_name):
            file_size = os.path.getsize(output_zip_name) / (1024 * 1024)  # MB
            logger.info(f"📦 Fichier ZIP créé: {output_zip_name} ({file_size:.1f} MB)")

            # Informations sur le contenu
            inference_instructions, inference_information = find_inference_files()
            if inference_instructions and os.path.exists(inference_instructions):
                logger.info("✅ Fichiers d'inférence inclus dans le ZIP:")
                logger.info("   - inference_instructions.txt")
                logger.info("   - inference_information.json")

            logger.info("💡 Vous pouvez maintenant transférer ce fichier sur un autre système")
            logger.info("💡 Pour l'installer: nnUNetv2_install_pretrained_model_from_zip FICHIER.zip")

            if best_config:
                logger.info(f"🎯 Ce modèle utilise la configuration optimale: {best_config}")
        else:
            logger.error(f"❌ Fichier ZIP non créé: {output_zip_name}")
            return False

    return success

def main():
    """Fonction principale d'exportation"""
    start_time = time.time()

    try:
        logger.info("📦 Début de l'exportation du modèle nnUNet")
        logger.info(f"📋 Configuration:")
        logger.info(f"   - Dataset ID: {DATASET_ID}")
        logger.info(f"   - Configuration: {CONFIGURATION}")
        logger.info(f"   - Époques: {EPOCHS}")
        logger.info(f"   - Trainer: nnUNetTrainer_{EPOCHS}epochs")
        logger.info(f"   - Fold: {FOLD}")
        logger.info(f"   - Plans: {PLANS_NAME}")

        # 1. Vérifier que le modèle existe
        if not check_model_exists():
            return False

        # 2. Exporter le modèle
        success = export_model()

        if not success:
            logger.error("❌ Échec de l'exportation")
            return False

        # Temps total
        total_time = time.time() - start_time
        logger.info(f"🎉 Exportation terminée en {total_time:.2f} secondes")

        return True

    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
