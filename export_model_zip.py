import os
import subprocess
import time
import logging
from pathlib import Path

# === CONFIGURATION (MODIFIEZ ICI - MÊME CONFIG QUE train_nnunet.py) ===
DATASET_ID = "029"
CONFIGURATION = "2d"      # '2d', '3d_fullres', '3d_lowres'
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5                # Doit correspondre au modèle entraîné
FOLD = "all"              # "all" pour ensemble, ou "0", "1", "2", "3", "4" pour un fold spécifique

# === CHEMINS (MÊME CONFIG QUE train_nnunet.py) ===
RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
RESULTS_PATH = "/mnt/results/nnUnet_results"

# === CONFIGURATION ENVIRONNEMENT ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH

# === LOGGING SIMPLE ===
def setup_logging():
    """Configure le logging simple"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"nnunet_export_{DATASET_ID}_{timestamp}.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

def run_command(cmd: str, description: str = "") -> bool:
    """Exécute une commande avec gestion d'erreurs"""
    logger.info(f"🟢 {description if description else 'Lancement'}: {cmd}")

    try:
        subprocess.run(cmd, shell=True, check=True)
        logger.info(f"✅ Succès: {description}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Erreur lors de: {description}")
        logger.error(f"Code de retour: {e.returncode}")
        return False

def check_model_exists() -> bool:
    """Vérifie que le modèle entraîné existe"""
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
    model_folder = os.path.join(RESULTS_PATH, f"Dataset{DATASET_ID}_*",
                               f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}")
    import glob
    folders = glob.glob(model_folder)

    if not folders:
        logger.error(f"❌ Modèle non trouvé: {model_folder}")
        logger.error("💡 Assurez-vous d'avoir entraîné le modèle avec train_nnunet.py")
        return False

    logger.info(f"✅ Modèle trouvé: {folders[0]}")

    # Vérifier les folds disponibles
    if FOLD == "all":
        fold_folders = [f for f in os.listdir(folders[0]) if f.startswith("fold_")]
        logger.info(f"📁 Folds disponibles: {fold_folders}")
    else:
        fold_folder = os.path.join(folders[0], f"fold_{FOLD}")
        if not os.path.exists(fold_folder):
            logger.error(f"❌ Fold {FOLD} non trouvé: {fold_folder}")
            return False
        logger.info(f"✅ Fold {FOLD} trouvé")

    return True

def export_model() -> bool:
    """Exporte le modèle en fichier ZIP"""
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
    output_zip_name = f"model_{DATASET_ID}_{CONFIGURATION}_{trainer_class}_fold{FOLD}.zip"

    # Construction de la commande
    cmd = (
        f"nnUNetv2_export_model_to_zip "
        f"-d {DATASET_ID} "
        f"-c {CONFIGURATION} "
        f"-tr {trainer_class} "
        f"-p {PLANS_NAME} "
        f"-f {FOLD} "
        f"-o {output_zip_name}"
    )

    success = run_command(cmd, f"Exportation du modèle vers {output_zip_name}")

    if success:
        # Vérifier que le fichier ZIP a été créé
        if os.path.exists(output_zip_name):
            file_size = os.path.getsize(output_zip_name) / (1024 * 1024)  # MB
            logger.info(f"📦 Fichier ZIP créé: {output_zip_name} ({file_size:.1f} MB)")
            logger.info("💡 Vous pouvez maintenant transférer ce fichier sur un autre système")
            logger.info("💡 Pour l'installer: nnUNetv2_install_pretrained_model_from_zip FICHIER.zip")
        else:
            logger.error(f"❌ Fichier ZIP non créé: {output_zip_name}")
            return False

    return success

def main():
    """Fonction principale d'exportation"""
    start_time = time.time()

    try:
        logger.info("📦 Début de l'exportation du modèle nnUNet")
        logger.info(f"📋 Configuration:")
        logger.info(f"   - Dataset ID: {DATASET_ID}")
        logger.info(f"   - Configuration: {CONFIGURATION}")
        logger.info(f"   - Époques: {EPOCHS}")
        logger.info(f"   - Trainer: nnUNetTrainer_{EPOCHS}epochs")
        logger.info(f"   - Fold: {FOLD}")
        logger.info(f"   - Plans: {PLANS_NAME}")

        # 1. Vérifier que le modèle existe
        if not check_model_exists():
            return False

        # 2. Exporter le modèle
        success = export_model()

        if not success:
            logger.error("❌ Échec de l'exportation")
            return False

        # Temps total
        total_time = time.time() - start_time
        logger.info(f"🎉 Exportation terminée en {total_time:.2f} secondes")

        return True

    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
