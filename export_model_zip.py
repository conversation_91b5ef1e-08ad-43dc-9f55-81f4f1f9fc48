import os
import subprocess

# === CONFIGURATION (MODIFIEZ ICI) ===
DATASET_ID = "029"
CONFIGURATION = "2d"      # '2d', '3d_fullres', '3d_lowres'
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5                # Doit correspondre au modèle entraîné
FOLDS = "0 1 2 3 4"       # Folds à exporter (séparés par des espaces)
OUTPUT_ZIP = f"model_{DATASET_ID}_{CONFIGURATION}_folds0to4.zip"

# === CHEMINS (MÊME CONFIG QUE train_nnunet.py) ===
RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
RESULTS_PATH = "/mnt/results/nnUnet_results"

# === CONFIGURATION ENVIRONNEMENT ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH

# === CONSTRUCTION DE LA COMMANDE ===
trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"

cmd = (
    f"nnUNetv2_export_model_to_zip "
    f"-d {DATASET_ID} "
    f"-o {OUTPUT_ZIP} "
    f"-c {CONFIGURATION} "
    f"-tr {trainer_class} "
    f"-p {PLANS_NAME} "
    f"-f {FOLDS}"
)

# === AFFICHAGE ET EXÉCUTION ===
print("🟢 Commande générée :")
print(cmd)
print(f"\n📦 Fichier de sortie : {OUTPUT_ZIP}")
print("\n⏳ Exportation en cours...\n")

try:
    subprocess.run(cmd, shell=True, check=True)
    print(f"\n✅ Exportation réussie : {OUTPUT_ZIP}")

    # Vérifier la taille du fichier
    if os.path.exists(OUTPUT_ZIP):
        file_size = os.path.getsize(OUTPUT_ZIP) / (1024 * 1024)  # MB
        print(f"📊 Taille du fichier : {file_size:.1f} MB")

except subprocess.CalledProcessError as e:
    print(f"\n❌ Échec de l'exportation.")
    print(f"Erreur : {e}")
    print(f"Code de retour : {e.returncode}")

print(f"\n💡 Pour installer sur un autre système :")
print(f"nnUNetv2_install_pretrained_model_from_zip {OUTPUT_ZIP}")
    """Vérifie que le modèle entraîné existe et lit les informations d'inférence"""
    inference_instructions, inference_information = find_inference_files()

    # Vérifier les fichiers d'inférence
    if inference_instructions and os.path.exists(inference_instructions):
        logger.info(f"✅ Fichier d'instructions trouvé: {inference_instructions}")

    if inference_information and os.path.exists(inference_information):
        logger.info(f"✅ Fichier d'informations trouvé: {inference_information}")

        # Lire les informations de performance
        try:
            import json
            with open(inference_information, 'r') as f:
                info = json.load(f)

            if 'foreground_mean' in info:
                logger.info(f"📊 Performance moyenne: {info['foreground_mean']}")

        except Exception as e:
            logger.warning(f"⚠️  Erreur lecture informations: {e}")

    # Vérification du modèle
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
    model_folder = os.path.join(RESULTS_PATH, f"Dataset{DATASET_ID}_*",
                               f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}")
    import glob
    folders = glob.glob(model_folder)

    if not folders:
        logger.error(f"❌ Modèle non trouvé: {model_folder}")
        logger.error("💡 Assurez-vous d'avoir entraîné le modèle avec train_nnunet.py")
        return False

    logger.info(f"✅ Modèle trouvé: {folders[0]}")

    # Vérifier les folds disponibles
    if FOLD == "all":
        fold_folders = [f for f in os.listdir(folders[0]) if f.startswith("fold_")]
        logger.info(f"📁 Folds disponibles: {fold_folders}")
    else:
        fold_folder = os.path.join(folders[0], f"fold_{FOLD}")
        if not os.path.exists(fold_folder):
            logger.error(f"❌ Fold {FOLD} non trouvé: {fold_folder}")
            return False
        logger.info(f"✅ Fold {FOLD} trouvé")

    return True

def copy_inference_files_to_zip_folder():
    """Copie les fichiers d'inférence dans le dossier temporaire avant création du ZIP"""
    inference_instructions, inference_information = find_inference_files()

    if inference_instructions and os.path.exists(inference_instructions):
        logger.info("📋 Ajout des fichiers d'inférence au modèle exporté")

        # Les fichiers seront inclus automatiquement par nnUNetv2_export_model_to_zip
        # car ils sont dans le dossier du dataset
        return True

    logger.warning("⚠️  Fichiers d'inférence non trouvés, export du modèle seulement")
    return True

def export_model() -> bool:
    """Exporte le modèle en fichier ZIP avec les fichiers d'inférence"""
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"

    # Obtenir la meilleure configuration si disponible
    best_config = get_best_configuration()
    if best_config:
        config_suffix = "_optimized"
    else:
        config_suffix = ""

    output_zip_name = f"model_{DATASET_ID}_{CONFIGURATION}_{trainer_class}_fold{FOLD}{config_suffix}.zip"

    # Vérifier et préparer les fichiers d'inférence
    copy_inference_files_to_zip_folder()

    # Construction de la commande
    cmd = (
        f"nnUNetv2_export_model_to_zip "
        f"-d {DATASET_ID} "
        f"-c {CONFIGURATION} "
        f"-tr {trainer_class} "
        f"-p {PLANS_NAME} "
        f"-f {FOLD} "
        f"-o {output_zip_name}"
    )

    success = run_command(cmd, f"Exportation du modèle vers {output_zip_name}")

    if success:
        # Vérifier que le fichier ZIP a été créé
        if os.path.exists(output_zip_name):
            file_size = os.path.getsize(output_zip_name) / (1024 * 1024)  # MB
            logger.info(f"📦 Fichier ZIP créé: {output_zip_name} ({file_size:.1f} MB)")

            # Informations sur le contenu
            inference_instructions, inference_information = find_inference_files()
            if inference_instructions and os.path.exists(inference_instructions):
                logger.info("✅ Fichiers d'inférence inclus dans le ZIP:")
                logger.info("   - inference_instructions.txt")
                logger.info("   - inference_information.json")

            logger.info("💡 Vous pouvez maintenant transférer ce fichier sur un autre système")
            logger.info("💡 Pour l'installer: nnUNetv2_install_pretrained_model_from_zip FICHIER.zip")

            if best_config:
                logger.info(f"🎯 Ce modèle utilise la configuration optimale: {best_config}")
        else:
            logger.error(f"❌ Fichier ZIP non créé: {output_zip_name}")
            return False

    return success

def main():
    """Fonction principale d'exportation"""
    start_time = time.time()

    try:
        logger.info("📦 Début de l'exportation du modèle nnUNet")
        logger.info(f"📋 Configuration:")
        logger.info(f"   - Dataset ID: {DATASET_ID}")
        logger.info(f"   - Configuration: {CONFIGURATION}")
        logger.info(f"   - Époques: {EPOCHS}")
        logger.info(f"   - Trainer: nnUNetTrainer_{EPOCHS}epochs")
        logger.info(f"   - Fold: {FOLD}")
        logger.info(f"   - Plans: {PLANS_NAME}")

        # 1. Vérifier que le modèle existe
        if not check_model_exists():
            return False

        # 2. Exporter le modèle
        success = export_model()

        if not success:
            logger.error("❌ Échec de l'exportation")
            return False

        # Temps total
        total_time = time.time() - start_time
        logger.info(f"🎉 Exportation terminée en {total_time:.2f} secondes")

        return True

    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
