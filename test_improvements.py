#!/usr/bin/env python3
"""
Script de test pour vérifier les améliorations du système nnUNet
"""

import sys
import os
from pathlib import Path
import importlib.util

def test_import(module_name: str, file_path: str) -> bool:
    """Teste l'import d'un module"""
    try:
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print(f"✅ {module_name}: Import réussi")
        return True
    except Exception as e:
        print(f"❌ {module_name}: Erreur d'import - {e}")
        return False

def test_config_functionality():
    """Teste les fonctionnalités de configuration"""
    try:
        from config_nnunet import nnUNetConfig, get_debug_config, get_quick_cv_config
        
        # Test configuration par défaut
        config = nnUNetConfig()
        assert config.dataset_id == "030"
        assert config.epochs == 750
        assert config.use_cross_validation == True
        print("✅ Configuration par défaut: OK")
        
        # Test configurations prédéfinies
        debug_config = get_debug_config()
        assert debug_config.epochs == 5
        assert debug_config.use_cross_validation == False
        print("✅ Configuration debug: OK")
        
        quick_config = get_quick_cv_config()
        assert quick_config.epochs == 100
        assert len(quick_config.validation_folds) == 2
        print("✅ Configuration quick CV: OK")
        
        # Test validation
        errors = config.validate()
        if not errors:
            print("✅ Validation de configuration: OK")
        else:
            print(f"⚠️  Validation: {errors}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test configuration: {e}")
        return False

def test_main_script_structure():
    """Teste la structure du script principal"""
    try:
        # Simuler l'import sans exécuter main()
        import train_nnunet
        
        # Vérifier que les fonctions principales existent
        required_functions = [
            'setup_logging',
            'run_command', 
            'find_dataset_folder',
            'run_preprocessing',
            'train_single_fold',
            'run_cross_validation',
            'run_ensemble_prediction'
        ]
        
        for func_name in required_functions:
            if hasattr(train_nnunet, func_name):
                print(f"✅ Fonction {func_name}: Trouvée")
            else:
                print(f"❌ Fonction {func_name}: Manquante")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Test script principal: {e}")
        return False

def test_file_structure():
    """Vérifie que tous les fichiers nécessaires existent"""
    required_files = [
        "train_nnunet.py",
        "config_nnunet.py", 
        "analyze_results.py",
        "example_usage.py",
        "README_improvements.md"
    ]
    
    all_exist = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}: Existe")
        else:
            print(f"❌ {file_path}: Manquant")
            all_exist = False
    
    return all_exist

def test_cross_validation_logic():
    """Teste la logique de validation croisée"""
    try:
        from config_nnunet import nnUNetConfig
        
        # Configuration avec validation croisée
        config = nnUNetConfig(use_cross_validation=True)
        assert config.validation_folds == [0, 1, 2, 3, 4]
        print("✅ Validation croisée: 5 folds configurés")
        
        # Configuration sans validation croisée  
        config_no_cv = nnUNetConfig(use_cross_validation=False)
        print("✅ Mode sans validation croisée: Configuré")
        
        # Test configuration personnalisée
        config_custom = nnUNetConfig(validation_folds=[0, 1])
        assert len(config_custom.validation_folds) == 2
        print("✅ Validation croisée personnalisée: OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Test validation croisée: {e}")
        return False

def test_logging_setup():
    """Teste la configuration du logging"""
    try:
        from train_nnunet import setup_logging
        
        logger = setup_logging("test")
        logger.info("Test de logging")
        
        # Vérifier que le dossier logs existe
        logs_dir = Path("logs")
        if logs_dir.exists():
            print("✅ Dossier logs: Créé")
        else:
            print("❌ Dossier logs: Non créé")
            return False
        
        print("✅ Configuration logging: OK")
        return True
        
    except Exception as e:
        print(f"❌ Test logging: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 Tests des améliorations nnUNet")
    print("=" * 50)
    
    tests = [
        ("Structure des fichiers", test_file_structure),
        ("Import config_nnunet", lambda: test_import("config_nnunet", "config_nnunet.py")),
        ("Import train_nnunet", lambda: test_import("train_nnunet", "train_nnunet.py")),
        ("Fonctionnalités configuration", test_config_functionality),
        ("Structure script principal", test_main_script_structure),
        ("Logique validation croisée", test_cross_validation_logic),
        ("Configuration logging", test_logging_setup)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Test: {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur inattendue: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("-" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nRésultat: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés ! Le système est prêt.")
        print("\n💡 Prochaines étapes:")
        print("   1. Adapter les chemins dans config_nnunet.py")
        print("   2. Tester avec: python train_nnunet.py --preset debug")
        print("   3. Lancer: python example_usage.py pour voir les exemples")
    else:
        print("⚠️  Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
