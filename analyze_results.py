"""
Script d'analyse des résultats d'entraînement nnUNet
Analyse les logs, métriques et performances de validation croisée
"""

import os
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Optional
import numpy as np
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class nnUNetResultsAnalyzer:
    """Analyseur de résultats nnUNet"""
    
    def __init__(self, results_path: str, dataset_id: str):
        self.results_path = Path(results_path)
        self.dataset_id = dataset_id
        self.dataset_folder = self.results_path / f"Dataset{dataset_id}_*"
        
    def find_dataset_results(self) -> Optional[Path]:
        """Trouve le dossier de résultats du dataset"""
        matching_folders = list(self.results_path.glob(f"Dataset{self.dataset_id}_*"))
        if not matching_folders:
            logger.error(f"Aucun dossier de résultats trouvé pour Dataset{self.dataset_id}")
            return None
        return matching_folders[0]
    
    def get_fold_results(self, configuration: str = "2d") -> Dict[int, Dict]:
        """Récupère les résultats de tous les folds"""
        dataset_folder = self.find_dataset_results()
        if not dataset_folder:
            return {}
        
        fold_results = {}
        trainer_folder = dataset_folder / f"nnUNetTrainer_*epochs__nnUNetPlans__{configuration}"
        
        for trainer_path in dataset_folder.glob(f"nnUNetTrainer_*epochs__nnUNetPlans__{configuration}"):
            for fold_path in trainer_path.glob("fold_*"):
                fold_num = int(fold_path.name.split("_")[1])
                
                # Lire les métriques de validation
                validation_file = fold_path / "validation" / "summary.json"
                training_log = fold_path / "training_log.txt"
                
                fold_data = {
                    "fold": fold_num,
                    "path": fold_path,
                    "validation_metrics": self._read_validation_metrics(validation_file),
                    "training_history": self._read_training_log(training_log)
                }
                
                fold_results[fold_num] = fold_data
        
        return fold_results
    
    def _read_validation_metrics(self, validation_file: Path) -> Dict:
        """Lit les métriques de validation"""
        if not validation_file.exists():
            return {}
        
        try:
            with open(validation_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"Erreur lecture {validation_file}: {e}")
            return {}
    
    def _read_training_log(self, log_file: Path) -> Dict:
        """Parse le log d'entraînement"""
        if not log_file.exists():
            return {}
        
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()
            
            epochs = []
            train_losses = []
            val_losses = []
            
            for line in lines:
                if "epoch:" in line and "train loss:" in line:
                    # Parse epoch, train loss, val loss
                    parts = line.split()
                    epoch = int(parts[1])
                    train_loss = float(parts[4])
                    if "val loss:" in line:
                        val_loss = float(parts[7])
                    else:
                        val_loss = None
                    
                    epochs.append(epoch)
                    train_losses.append(train_loss)
                    val_losses.append(val_loss)
            
            return {
                "epochs": epochs,
                "train_losses": train_losses,
                "val_losses": val_losses
            }
        except Exception as e:
            logger.warning(f"Erreur parsing {log_file}: {e}")
            return {}
    
    def create_summary_report(self, configuration: str = "2d") -> pd.DataFrame:
        """Crée un rapport de synthèse"""
        fold_results = self.get_fold_results(configuration)
        
        if not fold_results:
            logger.error("Aucun résultat trouvé")
            return pd.DataFrame()
        
        summary_data = []
        
        for fold_num, data in fold_results.items():
            metrics = data["validation_metrics"]
            
            row = {
                "Fold": fold_num,
                "Dice_Score": metrics.get("mean", {}).get("Dice", None),
                "IoU": metrics.get("mean", {}).get("IoU", None),
                "Precision": metrics.get("mean", {}).get("Precision", None),
                "Recall": metrics.get("mean", {}).get("Recall", None),
                "Training_Complete": len(data["training_history"].get("epochs", [])) > 0
            }
            
            summary_data.append(row)
        
        df = pd.DataFrame(summary_data)
        
        # Calculer les statistiques globales
        if len(df) > 0:
            logger.info("=== RÉSUMÉ VALIDATION CROISÉE ===")
            logger.info(f"Nombre de folds: {len(df)}")
            
            for metric in ["Dice_Score", "IoU", "Precision", "Recall"]:
                if metric in df.columns and df[metric].notna().any():
                    mean_val = df[metric].mean()
                    std_val = df[metric].std()
                    logger.info(f"{metric}: {mean_val:.4f} ± {std_val:.4f}")
        
        return df
    
    def plot_training_curves(self, configuration: str = "2d", save_path: Optional[str] = None):
        """Trace les courbes d'entraînement"""
        fold_results = self.get_fold_results(configuration)
        
        if not fold_results:
            logger.error("Aucun résultat trouvé pour tracer les courbes")
            return
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle(f"Courbes d'entraînement - Dataset {self.dataset_id} ({configuration})")
        
        for i, (fold_num, data) in enumerate(fold_results.items()):
            if i >= 5:  # Maximum 5 folds
                break
            
            row = i // 3
            col = i % 3
            ax = axes[row, col]
            
            history = data["training_history"]
            if history and "epochs" in history:
                epochs = history["epochs"]
                train_losses = history["train_losses"]
                val_losses = history["val_losses"]
                
                ax.plot(epochs, train_losses, label="Train Loss", color="blue")
                if val_losses and any(v is not None for v in val_losses):
                    val_losses_clean = [v for v in val_losses if v is not None]
                    epochs_clean = epochs[:len(val_losses_clean)]
                    ax.plot(epochs_clean, val_losses_clean, label="Val Loss", color="red")
                
                ax.set_title(f"Fold {fold_num}")
                ax.set_xlabel("Epoch")
                ax.set_ylabel("Loss")
                ax.legend()
                ax.grid(True)
        
        # Supprimer les axes vides
        for i in range(len(fold_results), 6):
            row = i // 3
            col = i % 3
            if row < 2 and col < 3:
                axes[row, col].remove()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")
            logger.info(f"Graphique sauvé: {save_path}")
        
        plt.show()
    
    def plot_metrics_comparison(self, configuration: str = "2d", save_path: Optional[str] = None):
        """Compare les métriques entre folds"""
        df = self.create_summary_report(configuration)
        
        if df.empty:
            logger.error("Aucune donnée pour tracer les métriques")
            return
        
        metrics = ["Dice_Score", "IoU", "Precision", "Recall"]
        available_metrics = [m for m in metrics if m in df.columns and df[m].notna().any()]
        
        if not available_metrics:
            logger.error("Aucune métrique disponible")
            return
        
        fig, ax = plt.subplots(figsize=(10, 6))
        
        x = np.arange(len(df))
        width = 0.2
        
        for i, metric in enumerate(available_metrics):
            values = df[metric].fillna(0)
            ax.bar(x + i * width, values, width, label=metric)
        
        ax.set_xlabel("Fold")
        ax.set_ylabel("Score")
        ax.set_title(f"Comparaison des métriques par fold - Dataset {self.dataset_id}")
        ax.set_xticks(x + width * (len(available_metrics) - 1) / 2)
        ax.set_xticklabels([f"Fold {f}" for f in df["Fold"]])
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")
            logger.info(f"Graphique sauvé: {save_path}")
        
        plt.show()

def main():
    """Fonction principale d'analyse"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Analyse des résultats nnUNet")
    parser.add_argument("--dataset_id", default="030", help="ID du dataset")
    parser.add_argument("--results_path", default="/mnt/results/nnUnet_results", 
                       help="Chemin vers les résultats")
    parser.add_argument("--config", default="2d", help="Configuration (2d, 3d_fullres)")
    parser.add_argument("--save_plots", action="store_true", help="Sauvegarder les graphiques")
    
    args = parser.parse_args()
    
    analyzer = nnUNetResultsAnalyzer(args.results_path, args.dataset_id)
    
    # Créer le rapport de synthèse
    df = analyzer.create_summary_report(args.config)
    
    if not df.empty:
        print("\n=== TABLEAU DE RÉSULTATS ===")
        print(df.to_string(index=False))
        
        # Sauvegarder le CSV
        csv_path = f"results_summary_dataset{args.dataset_id}_{args.config}.csv"
        df.to_csv(csv_path, index=False)
        logger.info(f"Résultats sauvés: {csv_path}")
    
    # Tracer les graphiques
    if args.save_plots:
        analyzer.plot_training_curves(args.config, 
                                    f"training_curves_dataset{args.dataset_id}_{args.config}.png")
        analyzer.plot_metrics_comparison(args.config, 
                                       f"metrics_comparison_dataset{args.dataset_id}_{args.config}.png")
    else:
        analyzer.plot_training_curves(args.config)
        analyzer.plot_metrics_comparison(args.config)

if __name__ == "__main__":
    main()
