#!/usr/bin/env python3
"""
Script de test pour vérifier que la configuration fonctionne correctement
"""

import sys
import os

def test_default_config():
    """Teste la configuration par défaut du fichier"""
    print("🔍 Test 1: Configuration par défaut du fichier")
    print("-" * 50)
    
    try:
        from config_nnunet import nnUNetConfig, print_current_config
        
        # Configuration par défaut
        config = nnUNetConfig()
        print_current_config(config)
        
        print("✅ Configuration par défaut chargée avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_config_with_no_args():
    """Teste la configuration sans arguments"""
    print("\n🔍 Test 2: Configuration sans arguments de ligne de commande")
    print("-" * 50)
    
    try:
        # Sauvegarder les arguments originaux
        original_argv = sys.argv.copy()
        
        # Simuler aucun argument
        sys.argv = ["test_script.py"]
        
        from config_nnunet import get_config_from_args, print_current_config
        
        config = get_config_from_args()
        print_current_config(config)
        
        # Restaurer les arguments
        sys.argv = original_argv
        
        print("✅ Configuration sans arguments chargée avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        # Restaurer les arguments en cas d'erreur
        sys.argv = original_argv
        return False

def test_config_with_args():
    """Teste la configuration avec arguments"""
    print("\n🔍 Test 3: Configuration avec arguments de ligne de commande")
    print("-" * 50)
    
    try:
        # Sauvegarder les arguments originaux
        original_argv = sys.argv.copy()
        
        # Simuler des arguments
        sys.argv = ["test_script.py", "--dataset_id", "999", "--epochs", "100", "--gpu", "1"]
        
        from config_nnunet import get_config_from_args, print_current_config
        
        config = get_config_from_args()
        print_current_config(config)
        
        # Vérifier que les arguments ont été pris en compte
        assert config.dataset_id == "999", f"Expected dataset_id=999, got {config.dataset_id}"
        assert config.epochs == 100, f"Expected epochs=100, got {config.epochs}"
        assert config.gpu_id == "1", f"Expected gpu_id=1, got {config.gpu_id}"
        
        # Restaurer les arguments
        sys.argv = original_argv
        
        print("✅ Configuration avec arguments chargée avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        # Restaurer les arguments en cas d'erreur
        sys.argv = original_argv
        return False

def test_preset_configs():
    """Teste les configurations prédéfinies"""
    print("\n🔍 Test 4: Configurations prédéfinies (presets)")
    print("-" * 50)
    
    try:
        from config_nnunet import get_debug_config, get_quick_cv_config, get_production_config
        
        # Test debug config
        debug_config = get_debug_config()
        print(f"Debug config: {debug_config.epochs} époques, CV={debug_config.use_cross_validation}")
        assert debug_config.epochs == 5
        assert debug_config.use_cross_validation == False
        
        # Test quick config
        quick_config = get_quick_cv_config()
        print(f"Quick config: {quick_config.epochs} époques, {len(quick_config.validation_folds)} folds")
        assert quick_config.epochs == 100
        assert len(quick_config.validation_folds) == 2
        
        # Test production config
        prod_config = get_production_config()
        print(f"Production config: {prod_config.epochs} époques, parallel={prod_config.parallel_folds}")
        assert prod_config.epochs == 1000
        assert prod_config.parallel_folds == True
        
        print("✅ Toutes les configurations prédéfinies fonctionnent")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_config_modification():
    """Teste la modification de la configuration"""
    print("\n🔍 Test 5: Modification de la configuration par défaut")
    print("-" * 50)
    
    try:
        from config_nnunet import nnUNetConfig
        
        # Créer une config et la modifier
        config = nnUNetConfig()
        original_epochs = config.epochs
        
        # Modifier
        config.epochs = 999
        config.dataset_id = "TEST"
        
        print(f"Configuration modifiée:")
        print(f"  Époques: {original_epochs} → {config.epochs}")
        print(f"  Dataset: 030 → {config.dataset_id}")
        
        assert config.epochs == 999
        assert config.dataset_id == "TEST"
        
        print("✅ Modification de configuration réussie")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_train_script_integration():
    """Teste l'intégration avec le script d'entraînement"""
    print("\n🔍 Test 6: Intégration avec train_nnunet.py")
    print("-" * 50)
    
    try:
        # Sauvegarder les arguments originaux
        original_argv = sys.argv.copy()
        
        # Simuler aucun argument pour utiliser la config par défaut
        sys.argv = ["train_nnunet.py"]
        
        # Importer le script principal
        import train_nnunet
        
        # Vérifier que les fonctions existent
        assert hasattr(train_nnunet, 'USE_CONFIG_FILE')
        assert train_nnunet.USE_CONFIG_FILE == True
        
        # Restaurer les arguments
        sys.argv = original_argv
        
        print("✅ Intégration avec train_nnunet.py réussie")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        # Restaurer les arguments en cas d'erreur
        sys.argv = original_argv
        return False

def show_usage_examples():
    """Affiche des exemples d'utilisation"""
    print("\n" + "=" * 60)
    print("💡 EXEMPLES D'UTILISATION")
    print("-" * 40)
    
    examples = [
        {
            "title": "Utiliser la configuration par défaut du fichier",
            "cmd": "python train_nnunet.py",
            "desc": "Utilise les valeurs définies dans config_nnunet.py"
        },
        {
            "title": "Modifier seulement certains paramètres",
            "cmd": "python train_nnunet.py --epochs 500 --gpu 1",
            "desc": "Garde la config par défaut mais change époques et GPU"
        },
        {
            "title": "Utiliser un preset",
            "cmd": "python train_nnunet.py --preset debug",
            "desc": "Utilise une configuration prédéfinie"
        },
        {
            "title": "Configuration complète",
            "cmd": "python train_nnunet.py --dataset_id 030 --config 2d --epochs 750",
            "desc": "Spécifie tous les paramètres manuellement"
        }
    ]
    
    for example in examples:
        print(f"\n📌 {example['title']}")
        print(f"   Commande: {example['cmd']}")
        print(f"   Description: {example['desc']}")

def main():
    """Fonction principale de test"""
    print("🧪 Tests de configuration nnUNet")
    print("=" * 60)
    
    tests = [
        ("Configuration par défaut", test_default_config),
        ("Sans arguments", test_config_with_no_args),
        ("Avec arguments", test_config_with_args),
        ("Configurations prédéfinies", test_preset_configs),
        ("Modification de config", test_config_modification),
        ("Intégration train_nnunet", test_train_script_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur inattendue dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("-" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nRésultat: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés !")
        print("\n💡 La configuration fonctionne correctement.")
        print("Vous pouvez maintenant modifier config_nnunet.py et les changements seront pris en compte.")
    else:
        print("⚠️  Certains tests ont échoué.")
    
    show_usage_examples()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
