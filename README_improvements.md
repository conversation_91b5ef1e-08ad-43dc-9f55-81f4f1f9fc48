# Améliorations du script d'entraînement nnUNet

## 🎯 Objectif

Transformation du script `train_nnunet.py` original pour implémenter une **validation croisée robuste** et des fonctionnalités avancées de monitoring et configuration.

## 📋 Problèmes identifiés dans le script original

### ❌ Problème principal : Pas de vraie validation
```python
FOLD = "all"  # ❌ Utilise toutes les données pour train ET validation
```
**Impact** : Pas de validation indépendante, risque de surapprentissage non détecté.

### ❌ Autres limitations
- Configuration hardcodée dans le script
- Pas de gestion d'erreurs robuste
- Pas de logging structuré
- Pas de monitoring des résultats
- Pas de parallélisation possible

## ✅ Améliorations apportées

### 1. **Validation croisée complète**
```python
# Avant (problématique)
FOLD = "all"  # Pas de vraie validation

# Après (correct)
USE_CROSS_VALIDATION = True
validation_folds = [0, 1, 2, 3, 4]  # 5-fold CV
```

### 2. **Configuration flexible**
- **Fichier de configuration** : `config_nnunet.py`
- **Arguments en ligne de commande**
- **Configurations prédéfinies** : debug, quick, production

### 3. **Gestion d'erreurs robuste**
```python
def run_command(cmd: str, description: str = "") -> bool:
    try:
        result = subprocess.run(cmd, shell=True, check=True, 
                              capture_output=True, text=True)
        logger.info(f"✅ Succès: {description}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Erreur: {e.stderr}")
        return False
```

### 4. **Logging avancé**
- Logs horodatés dans `logs/`
- Niveaux de log (INFO, WARNING, ERROR)
- Sortie console + fichier

### 5. **Parallélisation des folds**
```python
if config.parallel_folds and config.num_gpus > 1:
    with ThreadPoolExecutor() as executor:
        # Entraîner plusieurs folds en parallèle
```

### 6. **Analyse automatique des résultats**
- Script `analyze_results.py` pour visualiser les performances
- Graphiques de courbes d'entraînement
- Comparaison des métriques entre folds

## 📁 Structure des fichiers

```
├── train_nnunet.py          # Script principal amélioré
├── config_nnunet.py         # Configuration centralisée
├── analyze_results.py       # Analyse des résultats
├── example_usage.py         # Exemples d'utilisation
├── README_improvements.md   # Cette documentation
└── logs/                    # Dossier des logs (créé automatiquement)
```

## 🚀 Utilisation

### Configuration rapide
```bash
# Test rapide (5 époques)
python train_nnunet.py --preset debug

# Validation croisée rapide (2 folds)
python train_nnunet.py --preset quick

# Production complète
python train_nnunet.py --preset production
```

### Configuration personnalisée
```bash
python train_nnunet.py \
    --dataset_id 030 \
    --config 2d \
    --epochs 750 \
    --gpu 0,1 \
    --parallel
```

### Analyse des résultats
```bash
python analyze_results.py \
    --dataset_id 030 \
    --config 2d \
    --save_plots
```

## 📊 Validation croisée : Avant vs Après

### ❌ Avant (problématique)
```
Entraînement: 100% des données
Validation:   100% des données (identiques!)
Résultat:     Métriques biaisées, surapprentissage non détecté
```

### ✅ Après (correct)
```
Fold 0: Train 80% | Val 20% (cases 0-19)
Fold 1: Train 80% | Val 20% (cases 20-39)  
Fold 2: Train 80% | Val 20% (cases 40-59)
Fold 3: Train 80% | Val 20% (cases 60-79)
Fold 4: Train 80% | Val 20% (cases 80-99)

Résultat final: Moyenne ± écart-type sur 5 folds
```

## 🔧 Configuration avancée

### Modifier `config_nnunet.py`
```python
@dataclass
class nnUNetConfig:
    # Dataset
    dataset_id: str = "030"
    configuration: str = "2d"  # "2d", "3d_fullres", "3d_lowres"
    
    # Entraînement  
    epochs: int = 750
    use_cross_validation: bool = True  # ✅ Validation croisée
    parallel_folds: bool = False       # Parallélisation
    
    # GPU
    gpu_id: str = "0"                 # "0" ou "0,1,2,3"
    num_gpus: int = 1
    
    # Chemins (à adapter à votre environnement)
    raw_path: str = "/mnt/Datasets/nnUnet/nnUnet_raw"
    preprocessed_path: str = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
    results_path: str = "/mnt/results/nnUnet_results"
```

## 📈 Monitoring et analyse

### Logs en temps réel
```bash
tail -f logs/nnunet_training_030_*.log
```

### Analyse des résultats
Le script `analyze_results.py` génère :
- **Tableau de synthèse** : métriques par fold
- **Courbes d'entraînement** : loss par époque
- **Comparaison des folds** : Dice, IoU, Precision, Recall
- **Statistiques globales** : moyenne ± écart-type

### Exemple de sortie
```
=== RÉSUMÉ VALIDATION CROISÉE ===
Nombre de folds: 5
Dice_Score: 0.8542 ± 0.0123
IoU: 0.7834 ± 0.0156
Precision: 0.8721 ± 0.0098
Recall: 0.8456 ± 0.0134
```

## 🚨 Migration depuis l'ancien script

### Changements nécessaires
1. **Remplacer** `FOLD = "all"` par validation croisée
2. **Adapter les chemins** dans `config_nnunet.py`
3. **Tester** avec `--preset debug` d'abord

### Rétrocompatibilité
Pour garder l'ancien comportement :
```bash
python train_nnunet.py --no-cv  # Désactive la validation croisée
```

## 💡 Bonnes pratiques

### Workflow recommandé
1. **Test rapide** : `--preset debug` (5 époques)
2. **Validation** : `--preset quick` (2 folds)
3. **Analyse** : `analyze_results.py`
4. **Production** : `--preset production` (5 folds complets)
5. **Analyse finale** : graphiques et métriques

### Optimisation des performances
- **Multi-GPU** : `--gpu 0,1,2,3 --parallel`
- **Mémoire** : Réduire batch_size si OOM
- **Stockage** : Utiliser SSD pour les données

## 🔍 Dépannage

### Erreurs courantes
- **GPU non trouvé** : Vérifier `CUDA_VISIBLE_DEVICES`
- **Dataset non trouvé** : Vérifier les chemins dans config
- **Mémoire insuffisante** : Réduire `num_gpus` ou batch_size
- **Trainer non trouvé** : Vérifier que `nnUNetTrainer_750epochs` existe

### Support
- Logs détaillés dans `logs/`
- Messages d'erreur explicites
- Validation de configuration automatique

## 🎉 Résultats attendus

Avec ces améliorations, vous obtiendrez :
- ✅ **Validation robuste** avec 5-fold cross-validation
- ✅ **Métriques fiables** (moyenne ± écart-type)
- ✅ **Monitoring complet** (logs, graphiques)
- ✅ **Configuration flexible** (presets, arguments)
- ✅ **Gestion d'erreurs** robuste
- ✅ **Parallélisation** possible (multi-GPU)

La validation croisée vous donnera une **estimation beaucoup plus fiable** des performances réelles de votre modèle sur des données non vues.
