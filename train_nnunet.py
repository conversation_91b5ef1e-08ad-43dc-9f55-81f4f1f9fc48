import os
import subprocess
import time
import logging
from pathlib import Path
from typing import List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

# Import de la configuration
try:
    from config_nnunet import nnUNetConfig, get_config_from_args
    USE_CONFIG_FILE = True
except ImportError:
    USE_CONFIG_FILE = False
    # Configuration par défaut si config_nnunet.py n'est pas disponible
    class SimpleConfig:
        def __init__(self):
            self.dataset_id = "030"
            self.configuration = "2d"
            self.plans_name = "nnUNetPlans"
            self.epochs = 750
            self.gpu_id = "0"
            self.num_gpus = 1
            self.save_npz = True
            self.continue_training = True
            self.use_cross_validation = True
            self.parallel_folds = False
            self.ensemble_prediction = True
            self.raw_path = "/mnt/Datasets/nnUnet/nnUnet_raw"
            self.preprocessed_path = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
            self.results_path = "/mnt/results/nnUnet_results"
            self.validation_folds = [0, 1, 2, 3, 4]
            self.trainer_class = f"nnUNetTrainer_{self.epochs}epochs"

        def setup_environment(self):
            os.environ["nnUNet_raw"] = self.raw_path
            os.environ["nnUNet_preprocessed"] = self.preprocessed_path
            os.environ["nnUNet_results"] = self.results_path
            os.environ["CUDA_VISIBLE_DEVICES"] = self.gpu_id
            os.environ["nnUNet_n_proc_DA"] = "2"

# === LOGGING SETUP ===
def setup_logging(dataset_id: str = "unknown"):
    """Configure le système de logging"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"nnunet_training_{dataset_id}_{timestamp}.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

# Logger sera initialisé dans main()
logger = None

def run_command(cmd: str, description: str = "") -> bool:
    """Exécute une commande avec gestion d'erreurs améliorée"""
    logger.info(f"🟢 {description if description else 'Lancement'}: {cmd}")

    try:
        result = subprocess.run(cmd, shell=True, check=True,
                              capture_output=True, text=True)
        logger.info(f"✅ Succès: {description}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Erreur lors de: {description}")
        logger.error(f"Code de retour: {e.returncode}")
        logger.error(f"Stdout: {e.stdout}")
        logger.error(f"Stderr: {e.stderr}")
        return False

def find_dataset_folder(config) -> str:
    """Trouve automatiquement le dossier du dataset"""
    dataset_prefix = f"Dataset{config.dataset_id}_"
    try:
        dataset_name = next(
            (d for d in os.listdir(config.raw_path) if d.startswith(dataset_prefix)),
            None
        )
        if dataset_name is None:
            raise FileNotFoundError(f"❌ Aucun dossier commençant par '{dataset_prefix}' trouvé dans {config.raw_path}")

        images_dir = os.path.join(config.raw_path, dataset_name, "imagesTr")
        labels_dir = os.path.join(config.raw_path, dataset_name, "labelsTr")

        logger.info(f"📂 Dataset trouvé: {dataset_name}")
        logger.info(f"📂 images_dir = {images_dir}")
        logger.info(f"📂 labels_dir = {labels_dir}")

        return dataset_name
    except Exception as e:
        logger.error(f"Erreur lors de la recherche du dataset: {e}")
        raise

def run_preprocessing(config) -> bool:
    """Exécute le prétraitement nnUNet"""
    cmd = f'nnUNetv2_plan_and_preprocess -d {config.dataset_id} --verify_dataset_integrity'
    return run_command(cmd, "Prétraitement et vérification du dataset")

def train_single_fold(config, fold: int, gpu_id: Optional[str] = None) -> bool:
    """Entraîne un seul fold"""
    # Configuration GPU pour ce fold
    if gpu_id:
        original_gpu = os.environ.get("CUDA_VISIBLE_DEVICES", "")
        os.environ["CUDA_VISIBLE_DEVICES"] = gpu_id

    try:
        train_cmd = f'nnUNetv2_train {config.dataset_id} {config.configuration} {fold} -p {config.plans_name} -tr {config.trainer_class}'

        if config.save_npz:
            train_cmd += " --npz"
        if config.continue_training:
            train_cmd += " --c"
        if config.num_gpus > 1:
            train_cmd += f" -num_gpus {config.num_gpus}"

        success = run_command(train_cmd, f"Entraînement fold {fold}")

        if success:
            logger.info(f"✅ Fold {fold} terminé avec succès")
        else:
            logger.error(f"❌ Échec du fold {fold}")

        return success

    finally:
        # Restaurer la configuration GPU originale
        if gpu_id and 'original_gpu' in locals():
            os.environ["CUDA_VISIBLE_DEVICES"] = original_gpu

def run_cross_validation(config) -> bool:
    """Exécute la validation croisée complète"""
    folds = config.validation_folds
    successful_folds = []
    failed_folds = []

    logger.info(f"🔄 Début de la validation croisée sur {len(folds)} folds")

    if config.parallel_folds and config.num_gpus > 1:
        # Entraînement parallèle des folds (si plusieurs GPUs)
        available_gpus = config.gpu_id.split(',') if ',' in config.gpu_id else [config.gpu_id]

        with ThreadPoolExecutor(max_workers=min(len(folds), len(available_gpus))) as executor:
            future_to_fold = {}

            for i, fold in enumerate(folds):
                gpu_id = available_gpus[i % len(available_gpus)]
                future = executor.submit(train_single_fold, config, fold, gpu_id)
                future_to_fold[future] = fold

            for future in as_completed(future_to_fold):
                fold = future_to_fold[future]
                try:
                    success = future.result()
                    if success:
                        successful_folds.append(fold)
                    else:
                        failed_folds.append(fold)
                except Exception as e:
                    logger.error(f"Exception lors du fold {fold}: {e}")
                    failed_folds.append(fold)
    else:
        # Entraînement séquentiel des folds
        for fold in folds:
            logger.info(f"🚀 Début du fold {fold}")
            success = train_single_fold(config, fold)

            if success:
                successful_folds.append(fold)
            else:
                failed_folds.append(fold)

    # Résumé des résultats
    logger.info(f"📊 Résultats de la validation croisée:")
    logger.info(f"✅ Folds réussis: {successful_folds}")
    if failed_folds:
        logger.warning(f"❌ Folds échoués: {failed_folds}")

    return len(failed_folds) == 0

def run_ensemble_prediction(config) -> bool:
    """Crée les prédictions d'ensemble à partir de tous les folds"""
    if not config.ensemble_prediction:
        return True

    cmd = f'nnUNetv2_predict -i INPUT_FOLDER -o OUTPUT_FOLDER -d {config.dataset_id} -c {config.configuration} -p {config.plans_name}'
    logger.info("🎯 Création des prédictions d'ensemble...")
    logger.info("⚠️  N'oubliez pas de spécifier les dossiers INPUT_FOLDER et OUTPUT_FOLDER")
    logger.info(f"Commande d'ensemble: {cmd}")
    return True

def main():
    """Fonction principale"""
    global logger

    # 1. Charger la configuration
    if USE_CONFIG_FILE:
        try:
            config = get_config_from_args()
        except SystemExit:
            # Pas d'arguments fournis, utiliser config par défaut
            config = nnUNetConfig()
    else:
        config = SimpleConfig()

    # 2. Valider la configuration
    if hasattr(config, 'validate'):
        errors = config.validate()
        if errors:
            print("❌ Erreurs de configuration:")
            for error in errors:
                print(f"  - {error}")
            return False

    # 3. Configurer l'environnement
    config.setup_environment()

    # 4. Initialiser le logging
    logger = setup_logging(config.dataset_id)

    start_time = time.time()

    try:
        logger.info("🚀 Début de l'entraînement nnUNet")
        logger.info(f"📋 Configuration:")
        logger.info(f"   - Dataset ID: {config.dataset_id}")
        logger.info(f"   - Configuration: {config.configuration}")
        logger.info(f"   - Époques: {config.epochs}")
        logger.info(f"   - Validation croisée: {config.use_cross_validation}")
        logger.info(f"   - GPU(s): {config.gpu_id}")
        logger.info(f"   - Trainer: {config.trainer_class}")

        # 5. Trouver le dataset
        dataset_name = find_dataset_folder(config)

        # 6. Prétraitement
        if not run_preprocessing(config):
            logger.error("❌ Échec du prétraitement")
            return False

        # 7. Entraînement
        if config.use_cross_validation:
            success = run_cross_validation(config)
        else:
            # Entraînement sur toutes les données (mode original)
            logger.warning("⚠️  Mode 'all' - pas de vraie validation!")
            success = train_single_fold(config, "all")

        if not success:
            logger.error("❌ Échec de l'entraînement")
            return False

        # 8. Ensemble (optionnel)
        run_ensemble_prediction(config)

        # Temps total
        total_time = time.time() - start_time
        logger.info(f"🎉 Entraînement terminé en {total_time/3600:.2f} heures")

        # 9. Suggestion d'analyse
        logger.info("💡 Pour analyser les résultats:")
        logger.info(f"   python analyze_results.py --dataset_id {config.dataset_id} --config {config.configuration}")

        return True

    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
