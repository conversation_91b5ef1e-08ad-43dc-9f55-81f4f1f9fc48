#!/usr/bin/env python3
"""
Exemples d'utilisation du système d'entraînement nnUNet amélioré
"""

import subprocess
import sys
from pathlib import Path

def run_command(cmd: str, description: str):
    """Exécute une commande avec description"""
    print(f"\n🔹 {description}")
    print(f"Commande: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True)
        print("✅ Succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    print("🚀 Exemples d'utilisation du système d'entraînement nnUNet")
    print("=" * 60)
    
    # Vérifier que les fichiers existent
    required_files = ["train_nnunet.py", "config_nnunet.py", "analyze_results.py"]
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"❌ Fichiers manquants: {missing_files}")
        return False
    
    print("\n📋 EXEMPLES D'UTILISATION")
    print("-" * 40)
    
    examples = [
        {
            "title": "1. Entraînement debug (rapide pour tester)",
            "cmd": "python train_nnunet.py --preset debug",
            "description": "5 époques, pas de validation croisée, pour tester rapidement"
        },
        {
            "title": "2. Validation croisée rapide (2 folds)",
            "cmd": "python train_nnunet.py --preset quick",
            "description": "100 époques, validation croisée sur 2 folds seulement"
        },
        {
            "title": "3. Entraînement de production",
            "cmd": "python train_nnunet.py --preset production",
            "description": "1000 époques, validation croisée complète, multi-GPU"
        },
        {
            "title": "4. Configuration personnalisée",
            "cmd": "python train_nnunet.py --dataset_id 030 --config 2d --epochs 500 --gpu 0,1",
            "description": "Configuration manuelle avec paramètres spécifiques"
        },
        {
            "title": "5. Désactiver la validation croisée",
            "cmd": "python train_nnunet.py --no-cv --epochs 750",
            "description": "Entraînement sur toutes les données (mode original)"
        },
        {
            "title": "6. Entraînement 3D",
            "cmd": "python train_nnunet.py --config 3d_fullres --epochs 1000",
            "description": "Entraînement en 3D full resolution"
        },
        {
            "title": "7. Analyse des résultats",
            "cmd": "python analyze_results.py --dataset_id 030 --config 2d --save_plots",
            "description": "Analyser et visualiser les résultats d'entraînement"
        }
    ]
    
    for example in examples:
        print(f"\n{example['title']}")
        print(f"Description: {example['description']}")
        print(f"Commande: {example['cmd']}")
    
    print("\n" + "=" * 60)
    print("📊 WORKFLOW RECOMMANDÉ")
    print("-" * 40)
    
    workflow = [
        "1. Test rapide: python train_nnunet.py --preset debug",
        "2. Validation croisée: python train_nnunet.py --preset quick", 
        "3. Analyse: python analyze_results.py --dataset_id 030 --save_plots",
        "4. Production: python train_nnunet.py --preset production",
        "5. Analyse finale: python analyze_results.py --dataset_id 030 --save_plots"
    ]
    
    for step in workflow:
        print(f"   {step}")
    
    print("\n" + "=" * 60)
    print("⚙️  CONFIGURATION AVANCÉE")
    print("-" * 40)
    
    print("""
Pour une configuration avancée, modifiez config_nnunet.py:

1. Chemins des données:
   - raw_path: chemin vers nnUNet_raw
   - preprocessed_path: chemin vers nnUNet_preprocessed  
   - results_path: chemin vers nnUNet_results

2. Paramètres d'entraînement:
   - epochs: nombre d'époques
   - use_cross_validation: True/False
   - parallel_folds: entraînement parallèle des folds
   
3. GPU:
   - gpu_id: "0" ou "0,1,2,3" pour multi-GPU
   - num_gpus: nombre de GPUs à utiliser

4. Trainer personnalisé:
   - custom_trainer: "nnUNetTrainerAdam", etc.
""")
    
    print("\n" + "=" * 60)
    print("🔍 MONITORING ET LOGS")
    print("-" * 40)
    
    print("""
Les logs sont sauvés dans le dossier 'logs/':
- nnunet_training_[dataset_id]_[timestamp].log

Pour suivre l'entraînement en temps réel:
   tail -f logs/nnunet_training_030_*.log

Les résultats sont dans:
   /mnt/results/nnUnet_results/Dataset030_*/
""")
    
    print("\n" + "=" * 60)
    print("🚨 DÉPANNAGE")
    print("-" * 40)
    
    troubleshooting = [
        "❌ Erreur GPU: Vérifiez CUDA_VISIBLE_DEVICES",
        "❌ Erreur mémoire: Réduisez batch_size ou num_gpus", 
        "❌ Dataset non trouvé: Vérifiez les chemins dans config_nnunet.py",
        "❌ Trainer non trouvé: Vérifiez que nnUNetTrainer_Xepochs existe",
        "❌ Erreur preprocessing: Vérifiez l'intégrité du dataset"
    ]
    
    for tip in troubleshooting:
        print(f"   {tip}")
    
    print(f"\n✨ Prêt à commencer ! Lancez votre première commande.")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
