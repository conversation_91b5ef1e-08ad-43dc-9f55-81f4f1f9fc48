import os
import subprocess
from utils.visualisation import reconvert_predictions  # , generate_side_by_side
from utils.conversion import convert_images_to_grayscale
from utils.rename import rename_imagesTr, initial_rename
from utils.overlay_manager import OverlayManager
import cv2
import numpy as np
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === CONFIGURATION ===
DATASET_ID = "002"
CONFIGURATION = "2d"  # '2d' ou '3d_fullres'
FOLD = "all"
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5  # 👈 Choisis ici : 5, 10, 20, etc. ou 0 pour le trainer par défaut
GPU_ID = "0"

# === DOSSIERS ===
INPUT_FOLDER = "/home/<USER>/Dataset/imagesTestInference_RGB"  # À adapter si besoin

# Dossier de base pour les résultats
BASE_OUTPUT_ROOT = "/home/<USER>/results/inference"

# Nom basé sur le dossier d'entrée
input_name = os.path.basename(INPUT_FOLDER.rstrip("\\/")).replace(" ", "_")
output_candidate = os.path.join(BASE_OUTPUT_ROOT, input_name)

# 🔁 Ajouter suffixe _v2, _v3 si nécessaire
OUTPUT_FOLDER = output_candidate
version = 2
while os.path.exists(OUTPUT_FOLDER):
    OUTPUT_FOLDER = f"{output_candidate}_v{version}"
    version += 1

os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# === nnU-Net folders ===
RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
RESULTS_PATH = "/mnt/results/nnUnet_results"

# === ENVIRONNEMENT ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH
os.environ["CUDA_VISIBLE_DEVICES"] = GPU_ID


# === CONVERSION ET RENOMMAGE DES IMAGES D'ENTRÉE ===
print("\n🧠 Prétraitement des images d'entrée (format + couleur)...")

# 🔁 Conversion couleur → grayscale (non destructif, avec backup)
convert_images_to_grayscale(INPUT_FOLDER)

# 🔁 Première passe de renommage (format xxxx.png)
print("\n📝 Première passe de renommage...")
initial_rename(INPUT_FOLDER)

# 🔁 Deuxième passe de renommage (format xxxx_0000.png)
print("\n📝 Deuxième passe de renommage (format nnU-Net)...")
rename_imagesTr(INPUT_FOLDER)



# === CONSTRUCTION DE LA COMMANDE ===
trainer_class = f"nnUNetTrainer_{EPOCHS}epochs" if EPOCHS > 0 else None

cmd = f'nnUNetv2_predict -i "{INPUT_FOLDER}" -o "{OUTPUT_FOLDER}" -d {DATASET_ID} -c {CONFIGURATION} -f {FOLD}'
if trainer_class:
    cmd += f" -tr {trainer_class}"

# Facultatif :
# cmd += " --save_probabilities"

# === LANCEMENT ===
def run(cmd):
    print(f"\n🟢 Lancement : {cmd}\n")
    subprocess.run(cmd, shell=True, check=True)

print(f"📁 Dossier de sortie : {OUTPUT_FOLDER}")
run(cmd)


# === POST-INFERENCE : Reconversion + Visualisation ===

RECONVERTED_MASKS = os.path.join(OUTPUT_FOLDER, "reconverted_masks")
# SIDE_BY_SIDE_DIR = os.path.join(OUTPUT_FOLDER, "side_by_side")  # Désactivé
OVERLAY_DIR = os.path.join(OUTPUT_FOLDER, "overlays")

print("\n🎯 Post-traitement des masques prédits...")

reconvert_predictions(
    input_dir=OUTPUT_FOLDER,
    output_dir=RECONVERTED_MASKS,
    class_to_value={
        0: 0,    # background
        1: 29,   # frontwall
        2: 149,  # backwall
        3: 76,   # flaw
        4: 125   # indication
    }
)

# generate_side_by_side(
#     image_folder=INPUT_FOLDER,
#     mask_folder=RECONVERTED_MASKS,
#     output_dir=SIDE_BY_SIDE_DIR
# )

# Création des overlays
print("\n🎨 Création des overlays...")
os.makedirs(OVERLAY_DIR, exist_ok=True)
overlay_manager = OverlayManager()

# Afficher les fichiers disponibles
print("\n📁 Fichiers dans le dossier d'entrée:")
for f in os.listdir(INPUT_FOLDER):
    if f.endswith(('.png', '.jpg', '.jpeg')):
        print(f"- {f}")

print("\n📁 Fichiers dans le dossier des masques:")
for f in os.listdir(RECONVERTED_MASKS):
    if f.endswith('.png'):
        print(f"- {f}")

# Traiter chaque image
for img_name in os.listdir(INPUT_FOLDER):
    if img_name.endswith(('.png', '.jpg', '.jpeg')):
        try:
            # Charger l'image originale
            img_path = os.path.join(INPUT_FOLDER, img_name)
            logger.info(f"Traitement de l'image: {img_name}")
            
            original_img = cv2.imread(img_path)
            if original_img is None:
                logger.error(f"Impossible de charger l'image: {img_path}")
                continue
                
            # Charger le masque correspondant
            # Extraire le numéro de l'image (ex: "0001" de "0001_0000.png")
            img_number = img_name.split('_')[0]
            mask_name = f"{img_number}.png"  # Format attendu par nnU-Net
            mask_path = os.path.join(RECONVERTED_MASKS, mask_name)
            
            if not os.path.exists(mask_path):
                logger.error(f"Masque non trouvé: {mask_path}")
                continue
                
            mask_img = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
            if mask_img is None:
                logger.error(f"Impossible de charger le masque: {mask_path}")
                continue
            
            logger.info(f"Image originale: {original_img.shape}, Masque: {mask_img.shape}")
            
            # Créer l'overlay avec couleurs corrigées (rouge et vert inversés)
            overlay = overlay_manager.create_high_contrast_overlay(original_img, mask_img, alpha=0.8)
            
            # Sauvegarder l'overlay
            output_path = os.path.join(OVERLAY_DIR, f"overlay_{img_name}")
            overlay_manager.save_overlay(overlay, output_path)
            logger.info(f"Overlay sauvegardé: {output_path}")
            
        except Exception as e:
            logger.error(f"Erreur lors du traitement de {img_name}: {str(e)}")
            continue

print("\n✅ Inférence et post-traitement terminés.")
