import os
import subprocess
import time
import logging
from pathlib import Path

# === CONFIGURATION (MODIFIEZ ICI) ===
DATASET_ID = "029"
CONFIGURATION = "2d"      # '2d', '3d_fullres', '3d_lowres'
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5                # Doit correspondre au modèle entraîné
GPU_ID = "0"              # "0" ou "0,1,2,3" pour multi-GPU

# === DOSSIERS D'INFÉRENCE ===
INPUT_FOLDER = "/path/to/your/input/images"     # MODIFIEZ : Dossier contenant les images à prédire
OUTPUT_FOLDER = "/path/to/your/output/results"  # MODIFIEZ : Dossier de sortie des prédictions

# === CHEMINS nnUNet (MÊME CONFIG QUE train_nnunet.py) ===
RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
RESULTS_PATH = "/mnt/results/nnUnet_results"

# === CONFIGURATION ENVIRONNEMENT ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH
os.environ["CUDA_VISIBLE_DEVICES"] = GPU_ID

# === LOGGING SIMPLE ===
def setup_logging():
    """Configure le logging simple"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"nnunet_inference_{DATASET_ID}_{timestamp}.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

def run_command(cmd: str, description: str = "") -> bool:
    """Exécute une commande avec gestion d'erreurs"""
    logger.info(f"🟢 {description if description else 'Lancement'}: {cmd}")

    try:
        subprocess.run(cmd, shell=True, check=True)
        logger.info(f"✅ Succès: {description}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Erreur lors de: {description}")
        logger.error(f"Code de retour: {e.returncode}")
        return False

def check_model_exists() -> bool:
    """Vérifie que le modèle entraîné existe"""
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
    model_folder = os.path.join(RESULTS_PATH, f"Dataset{DATASET_ID}_*",
                               f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}")
    import glob
    folders = glob.glob(model_folder)

    if not folders:
        logger.error(f"❌ Modèle non trouvé: {model_folder}")
        logger.error("💡 Assurez-vous d'avoir entraîné le modèle avec train_nnunet.py")
        return False

    logger.info(f"✅ Modèle trouvé: {folders[0]}")
    return True

def check_input_folder() -> bool:
    """Vérifie que le dossier d'entrée existe et contient des images"""
    if not os.path.exists(INPUT_FOLDER):
        logger.error(f"❌ Dossier d'entrée non trouvé: {INPUT_FOLDER}")
        return False

    # Compter les images compatibles nnUNet
    image_files = [f for f in os.listdir(INPUT_FOLDER)
                   if f.endswith(('.nii.gz', '.nii', '.png', '.tif', '.tiff'))]

    if not image_files:
        logger.error(f"❌ Aucune image trouvée dans: {INPUT_FOLDER}")
        logger.error("💡 Formats supportés: .nii.gz, .nii, .png, .tif, .tiff")
        return False

    logger.info(f"✅ {len(image_files)} images trouvées dans le dossier d'entrée")
    return True

def run_inference() -> bool:
    """Exécute l'inférence nnUNet"""
    # Créer le dossier de sortie
    os.makedirs(OUTPUT_FOLDER, exist_ok=True)

    # Construire la commande d'inférence
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
    cmd = f'nnUNetv2_predict -i "{INPUT_FOLDER}" -o "{OUTPUT_FOLDER}" -d {DATASET_ID} -c {CONFIGURATION} -tr {trainer_class}'

    # Utiliser tous les folds (ensemble automatique)
    # cmd += " -f all"  # Par défaut, utilise tous les folds disponibles

    success = run_command(cmd, "Inférence nnUNet")

    if success:
        logger.info(f"📁 Résultats sauvés dans: {OUTPUT_FOLDER}")

        # Lister les fichiers générés
        if os.path.exists(OUTPUT_FOLDER):
            result_files = os.listdir(OUTPUT_FOLDER)
            logger.info(f"📊 {len(result_files)} fichiers générés:")
            for f in result_files[:5]:  # Afficher les 5 premiers
                logger.info(f"   - {f}")
            if len(result_files) > 5:
                logger.info(f"   ... et {len(result_files) - 5} autres")

    return success

def main():
    """Fonction principale d'inférence"""
    start_time = time.time()

    try:
        logger.info("🔮 Début de l'inférence nnUNet")
        logger.info(f"📋 Configuration:")
        logger.info(f"   - Dataset ID: {DATASET_ID}")
        logger.info(f"   - Configuration: {CONFIGURATION}")
        logger.info(f"   - Époques: {EPOCHS}")
        logger.info(f"   - Trainer: nnUNetTrainer_{EPOCHS}epochs")
        logger.info(f"   - GPU: {GPU_ID}")
        logger.info(f"   - Input: {INPUT_FOLDER}")
        logger.info(f"   - Output: {OUTPUT_FOLDER}")

        # 1. Vérifications préliminaires
        if not check_model_exists():
            return False

        if not check_input_folder():
            return False

        # 2. Exécuter l'inférence
        success = run_inference()

        if not success:
            logger.error("❌ Échec de l'inférence")
            return False

        # Temps total
        total_time = time.time() - start_time
        logger.info(f"🎉 Inférence terminée en {total_time/60:.2f} minutes")

        return True

    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
