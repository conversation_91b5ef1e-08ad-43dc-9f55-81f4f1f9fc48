"""
Configuration centralisée pour l'entraînement nnUNet
Modifiez ce fichier pour ajuster les paramètres d'entraînement
"""

import os
from dataclasses import dataclass
from typing import List, Optional

@dataclass
class nnUNetConfig:
    """Configuration pour l'entraînement nnUNet"""
    
    # === DATASET ===
    dataset_id: str = "030"
    configuration: str = "2d"  # '2d', '3d_fullres', '3d_lowres'
    plans_name: str = "nnUNetPlans"
    
    # === ENTRAÎNEMENT ===
    epochs: int = 750
    use_cross_validation: bool = True  # True pour CV, False pour "all"
    parallel_folds: bool = False       # Entraîner folds en parallèle
    continue_training: bool = True
    save_npz: bool = True
    
    # === GPU ===
    gpu_id: str = "0"                 # "0" ou "0,1,2,3" pour multi-GPU
    num_gpus: int = 1
    
    # === CHEMINS ===
    raw_path: str = "/mnt/Datasets/nnUnet/nnUnet_raw"
    preprocessed_path: str = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
    results_path: str = "/mnt/results/nnUnet_results"
    
    # === TRAINER PERSONNALISÉ ===
    custom_trainer: Optional[str] = None  # Ex: "nnUNetTrainerAdam"
    
    # === POST-TRAITEMENT ===
    ensemble_prediction: bool = True
    
    # === VALIDATION ===
    validation_folds: List[int] = None  # Par défaut [0,1,2,3,4]
    
    def __post_init__(self):
        """Initialisation après création"""
        if self.validation_folds is None:
            self.validation_folds = [0, 1, 2, 3, 4]
    
    @property
    def trainer_class(self) -> str:
        """Retourne le nom de la classe trainer"""
        if self.custom_trainer:
            return self.custom_trainer
        return f"nnUNetTrainer_{self.epochs}epochs"
    
    def setup_environment(self):
        """Configure les variables d'environnement"""
        os.environ["nnUNet_raw"] = self.raw_path
        os.environ["nnUNet_preprocessed"] = self.preprocessed_path
        os.environ["nnUNet_results"] = self.results_path
        os.environ["CUDA_VISIBLE_DEVICES"] = self.gpu_id
        os.environ["nnUNet_n_proc_DA"] = "2"
    
    def validate(self) -> List[str]:
        """Valide la configuration et retourne les erreurs"""
        errors = []
        
        if not os.path.exists(self.raw_path):
            errors.append(f"Chemin raw inexistant: {self.raw_path}")
        
        if self.configuration not in ["2d", "3d_fullres", "3d_lowres"]:
            errors.append(f"Configuration invalide: {self.configuration}")
        
        if self.epochs <= 0:
            errors.append(f"Nombre d'époques invalide: {self.epochs}")
        
        if self.num_gpus <= 0:
            errors.append(f"Nombre de GPUs invalide: {self.num_gpus}")
        
        return errors

# === CONFIGURATIONS PRÉDÉFINIES ===

def get_debug_config() -> nnUNetConfig:
    """Configuration pour debug/test rapide"""
    return nnUNetConfig(
        epochs=5,
        use_cross_validation=False,
        save_npz=False,
        ensemble_prediction=False
    )

def get_quick_cv_config() -> nnUNetConfig:
    """Configuration pour validation croisée rapide"""
    return nnUNetConfig(
        epochs=100,
        use_cross_validation=True,
        parallel_folds=False,
        validation_folds=[0, 1]  # Seulement 2 folds pour test
    )

def get_production_config() -> nnUNetConfig:
    """Configuration pour entraînement de production"""
    return nnUNetConfig(
        epochs=1000,
        use_cross_validation=True,
        parallel_folds=True,
        num_gpus=2,
        gpu_id="0,1"
    )

def get_config_from_args() -> nnUNetConfig:
    """Crée une configuration à partir des arguments de ligne de commande"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Configuration nnUNet")
    parser.add_argument("--dataset_id", default="030", help="ID du dataset")
    parser.add_argument("--config", default="2d", choices=["2d", "3d_fullres", "3d_lowres"])
    parser.add_argument("--epochs", type=int, default=750, help="Nombre d'époques")
    parser.add_argument("--no-cv", action="store_true", help="Désactiver validation croisée")
    parser.add_argument("--parallel", action="store_true", help="Folds en parallèle")
    parser.add_argument("--gpu", default="0", help="GPU ID(s)")
    parser.add_argument("--preset", choices=["debug", "quick", "production"], 
                       help="Configuration prédéfinie")
    
    args = parser.parse_args()
    
    # Configuration prédéfinie
    if args.preset == "debug":
        config = get_debug_config()
    elif args.preset == "quick":
        config = get_quick_cv_config()
    elif args.preset == "production":
        config = get_production_config()
    else:
        config = nnUNetConfig()
    
    # Surcharger avec les arguments
    config.dataset_id = args.dataset_id
    config.configuration = args.config
    config.epochs = args.epochs
    config.use_cross_validation = not args.no_cv
    config.parallel_folds = args.parallel
    config.gpu_id = args.gpu
    config.num_gpus = len(args.gpu.split(','))
    
    return config

# Configuration par défaut
DEFAULT_CONFIG = nnUNetConfig()
