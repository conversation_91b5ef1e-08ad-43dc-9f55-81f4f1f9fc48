#!/usr/bin/env python3
"""
Exemple de modification de la configuration nnUNet
Montre comment personnaliser config_nnunet.py pour vos besoins
"""

def show_current_config():
    """Affiche la configuration actuelle"""
    print("🔍 Configuration actuelle dans config_nnunet.py:")
    print("=" * 60)
    
    try:
        from config_nnunet import nnUNetConfig, print_current_config
        
        config = nnUNetConfig()
        print_current_config(config)
        
        return config
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def show_how_to_modify():
    """Montre comment modifier la configuration"""
    print("\n" + "=" * 60)
    print("📝 COMMENT MODIFIER LA CONFIGURATION")
    print("-" * 40)
    
    print("""
Pour personnaliser votre configuration, modifiez directement le fichier config_nnunet.py.

🔧 ÉTAPES:

1. Ouvrez config_nnunet.py dans votre éditeur
2. Modifiez la classe nnUNetConfig (lignes ~15-50)
3. Sauvegardez le fichier
4. Lancez: python train_nnunet.py (sans arguments)

📋 PARAMÈTRES PRINCIPAUX À MODIFIER:

@dataclass
class nnUNetConfig:
    # === DATASET ===
    dataset_id: str = "030"           # ← Changez votre ID de dataset
    configuration: str = "2d"         # ← "2d", "3d_fullres", "3d_lowres"
    
    # === ENTRAÎNEMENT ===
    epochs: int = 750                 # ← Nombre d'époques
    use_cross_validation: bool = True # ← True pour CV, False pour "all"
    
    # === GPU ===
    gpu_id: str = "0"                # ← "0" ou "0,1,2,3" pour multi-GPU
    num_gpus: int = 1                # ← Nombre de GPUs
    
    # === CHEMINS (IMPORTANT!) ===
    raw_path: str = "/mnt/Datasets/nnUnet/nnUnet_raw"
    preprocessed_path: str = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"  
    results_path: str = "/mnt/results/nnUnet_results"
""")

def show_common_modifications():
    """Montre des modifications courantes"""
    print("\n" + "=" * 60)
    print("🎯 MODIFICATIONS COURANTES")
    print("-" * 40)
    
    modifications = [
        {
            "title": "1. Changer le dataset",
            "before": 'dataset_id: str = "030"',
            "after": 'dataset_id: str = "027"  # Pour ACDC par exemple',
            "desc": "Modifie l'ID du dataset à entraîner"
        },
        {
            "title": "2. Réduire les époques pour test",
            "before": 'epochs: int = 750',
            "after": 'epochs: int = 100  # Test rapide',
            "desc": "Réduit le temps d'entraînement pour tester"
        },
        {
            "title": "3. Utiliser plusieurs GPUs",
            "before": 'gpu_id: str = "0"\n    num_gpus: int = 1',
            "after": 'gpu_id: str = "0,1,2,3"\n    num_gpus: int = 4',
            "desc": "Active l'entraînement multi-GPU"
        },
        {
            "title": "4. Changer les chemins (Windows)",
            "before": 'raw_path: str = "/mnt/Datasets/nnUnet/nnUnet_raw"',
            "after": 'raw_path: str = "C:/Data/nnUnet/nnUnet_raw"',
            "desc": "Adapte les chemins pour Windows"
        },
        {
            "title": "5. Désactiver la validation croisée",
            "before": 'use_cross_validation: bool = True',
            "after": 'use_cross_validation: bool = False',
            "desc": "Revient au mode original (FOLD='all')"
        },
        {
            "title": "6. Activer l'entraînement parallèle",
            "before": 'parallel_folds: bool = False',
            "after": 'parallel_folds: bool = True',
            "desc": "Entraîne plusieurs folds en parallèle"
        }
    ]
    
    for mod in modifications:
        print(f"\n{mod['title']}")
        print(f"Description: {mod['desc']}")
        print(f"Avant:  {mod['before']}")
        print(f"Après:  {mod['after']}")

def show_example_configs():
    """Montre des exemples de configurations complètes"""
    print("\n" + "=" * 60)
    print("📋 EXEMPLES DE CONFIGURATIONS COMPLÈTES")
    print("-" * 40)
    
    configs = [
        {
            "name": "Configuration pour test rapide",
            "config": """
@dataclass
class nnUNetConfig:
    dataset_id: str = "030"
    configuration: str = "2d"
    epochs: int = 50                    # ← Réduit pour test
    use_cross_validation: bool = False  # ← Pas de CV pour test
    gpu_id: str = "0"
    raw_path: str = "/votre/chemin/nnUnet_raw"
    preprocessed_path: str = "/votre/chemin/nnUnet_preprocessed"
    results_path: str = "/votre/chemin/nnUnet_results"
"""
        },
        {
            "name": "Configuration pour production (multi-GPU)",
            "config": """
@dataclass  
class nnUNetConfig:
    dataset_id: str = "030"
    configuration: str = "2d"
    epochs: int = 1000                  # ← Plus d'époques
    use_cross_validation: bool = True   # ← Validation croisée
    parallel_folds: bool = True         # ← Folds en parallèle
    gpu_id: str = "0,1,2,3"            # ← 4 GPUs
    num_gpus: int = 4
    raw_path: str = "/votre/chemin/nnUnet_raw"
    preprocessed_path: str = "/votre/chemin/nnUnet_preprocessed"
    results_path: str = "/votre/chemin/nnUnet_results"
"""
        },
        {
            "name": "Configuration pour Windows",
            "config": """
@dataclass
class nnUNetConfig:
    dataset_id: str = "030"
    configuration: str = "2d"
    epochs: int = 750
    use_cross_validation: bool = True
    gpu_id: str = "0"
    raw_path: str = "C:/Data/nnUnet/nnUnet_raw"           # ← Chemins Windows
    preprocessed_path: str = "C:/Data/nnUnet/nnUnet_preprocessed"
    results_path: str = "C:/Data/nnUnet/nnUnet_results"
"""
        }
    ]
    
    for config in configs:
        print(f"\n🔧 {config['name']}")
        print(config['config'])

def show_testing_workflow():
    """Montre le workflow de test"""
    print("\n" + "=" * 60)
    print("🧪 WORKFLOW DE TEST RECOMMANDÉ")
    print("-" * 40)
    
    steps = [
        "1. Modifiez config_nnunet.py avec vos paramètres",
        "2. Testez la config: python test_config.py",
        "3. Test rapide: python train_nnunet.py --preset debug",
        "4. Vérifiez les logs dans logs/",
        "5. Si OK, lancez: python train_nnunet.py (utilise votre config)",
        "6. Analysez: python analyze_results.py --dataset_id VOTRE_ID"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print(f"\n💡 VÉRIFICATION:")
    print(f"   - Les chemins existent et sont accessibles")
    print(f"   - Le dataset est au bon format nnUNet")
    print(f"   - Les GPUs sont disponibles")
    print(f"   - Assez d'espace disque pour les résultats")

def main():
    """Fonction principale"""
    print("🔧 Guide de modification de la configuration nnUNet")
    
    # Afficher la config actuelle
    config = show_current_config()
    
    if config:
        # Guides de modification
        show_how_to_modify()
        show_common_modifications()
        show_example_configs()
        show_testing_workflow()
        
        print("\n" + "=" * 60)
        print("✨ RÉSUMÉ")
        print("-" * 40)
        print("1. Modifiez config_nnunet.py selon vos besoins")
        print("2. Lancez python train_nnunet.py (sans arguments)")
        print("3. Vos modifications seront automatiquement prises en compte")
        print("\n🎯 Plus besoin de modifier le script principal !")
        
    else:
        print("❌ Impossible de charger la configuration")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
