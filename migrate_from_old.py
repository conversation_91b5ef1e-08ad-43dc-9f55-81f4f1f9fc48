#!/usr/bin/env python3
"""
Script de migration depuis l'ancien train_nnunet.py vers le nouveau système
"""

import os
import shutil
from pathlib import Path
import re

def backup_old_script():
    """Sauvegarde l'ancien script"""
    old_script = Path("train_nnunet.py")
    
    if old_script.exists():
        # Vérifier si c'est l'ancien script (chercher FOLD = "all")
        with open(old_script, 'r') as f:
            content = f.read()
        
        if 'FOLD = "all"' in content and 'def main():' not in content:
            backup_path = Path("train_nnunet_old_backup.py")
            shutil.copy2(old_script, backup_path)
            print(f"✅ Ancien script sauvegardé: {backup_path}")
            return True
        else:
            print("ℹ️  Le script semble déjà être la nouvelle version")
            return False
    else:
        print("⚠️  Aucun train_nnunet.py trouvé")
        return False

def extract_old_config():
    """Extrait la configuration de l'ancien script"""
    old_script = Path("train_nnunet_old_backup.py")
    
    if not old_script.exists():
        print("❌ Fichier de sauvegarde non trouvé")
        return None
    
    config = {}
    
    with open(old_script, 'r') as f:
        content = f.read()
    
    # Extraire les variables de configuration
    patterns = {
        'dataset_id': r'DATASET_ID\s*=\s*["\'](\w+)["\']',
        'configuration': r'CONFIGURATION\s*=\s*["\'](\w+)["\']',
        'epochs': r'EPOCHS\s*=\s*(\d+)',
        'gpu_id': r'GPU_ID\s*=\s*["\']([^"\']+)["\']',
        'num_gpus': r'NUM_GPUS\s*=\s*(\d+)',
        'raw_path': r'RAW_PATH\s*=\s*["\']([^"\']+)["\']',
        'preprocessed_path': r'PREPROCESSED_PATH\s*=\s*["\']([^"\']+)["\']',
        'results_path': r'RESULTS_PATH\s*=\s*["\']([^"\']+)["\']',
    }
    
    for key, pattern in patterns.items():
        match = re.search(pattern, content)
        if match:
            value = match.group(1)
            # Convertir en int si nécessaire
            if key in ['epochs', 'num_gpus']:
                value = int(value)
            config[key] = value
    
    return config

def create_migrated_config(old_config):
    """Crée un fichier de configuration basé sur l'ancien script"""
    if not old_config:
        print("❌ Impossible de créer la configuration")
        return False
    
    config_content = f'''"""
Configuration migrée depuis l'ancien train_nnunet.py
Générée automatiquement par migrate_from_old.py
"""

from config_nnunet import nnUNetConfig

# Configuration migrée
MIGRATED_CONFIG = nnUNetConfig(
    dataset_id="{old_config.get('dataset_id', '030')}",
    configuration="{old_config.get('configuration', '2d')}",
    epochs={old_config.get('epochs', 750)},
    gpu_id="{old_config.get('gpu_id', '0')}",
    num_gpus={old_config.get('num_gpus', 1)},
    raw_path="{old_config.get('raw_path', '/mnt/Datasets/nnUnet/nnUnet_raw')}",
    preprocessed_path="{old_config.get('preprocessed_path', '/mnt/Datasets/nnUnet/nnUnet_preprocessed')}",
    results_path="{old_config.get('results_path', '/mnt/results/nnUnet_results')}",
    
    # Nouvelles options (recommandées)
    use_cross_validation=True,  # ✅ Validation croisée au lieu de FOLD="all"
    parallel_folds=False,
    ensemble_prediction=True,
    save_npz=True,
    continue_training=True
)

def get_migrated_config():
    """Retourne la configuration migrée"""
    return MIGRATED_CONFIG
'''
    
    config_file = Path("migrated_config.py")
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    print(f"✅ Configuration migrée créée: {config_file}")
    return True

def create_migration_script(old_config):
    """Crée un script de migration personnalisé"""
    script_content = f'''#!/usr/bin/env python3
"""
Script de migration personnalisé
Utilise votre ancienne configuration avec les nouvelles fonctionnalités
"""

from migrated_config import get_migrated_config
import sys
import os

def main():
    """Utilise la configuration migrée"""
    
    # Charger la configuration migrée
    config = get_migrated_config()
    
    print("🔄 Migration de votre ancienne configuration")
    print("=" * 50)
    print(f"Dataset ID: {{config.dataset_id}}")
    print(f"Configuration: {{config.configuration}}")
    print(f"Époques: {{config.epochs}}")
    print(f"GPU: {{config.gpu_id}}")
    print(f"Validation croisée: {{config.use_cross_validation}}")
    print()
    
    # Configurer l'environnement
    config.setup_environment()
    
    # Importer et lancer le nouveau système
    try:
        from train_nnunet import main as train_main
        
        # Remplacer la configuration globale
        import train_nnunet
        train_nnunet.USE_CONFIG_FILE = True
        
        # Simuler les arguments pour utiliser notre config
        original_argv = sys.argv
        sys.argv = ["train_nnunet.py"]  # Pas d'arguments = config par défaut
        
        # Monkey patch pour utiliser notre config
        def get_our_config():
            return config
        
        if hasattr(train_nnunet, 'get_config_from_args'):
            train_nnunet.get_config_from_args = get_our_config
        
        # Lancer l'entraînement
        success = train_main()
        
        # Restaurer
        sys.argv = original_argv
        
        if success:
            print("🎉 Migration et entraînement réussis !")
        else:
            print("❌ Erreur lors de l'entraînement")
            
        return success
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {{e}}")
        print("Assurez-vous que train_nnunet.py est présent")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
    
    script_file = Path("run_migrated.py")
    with open(script_file, 'w') as f:
        f.write(script_content)
    
    print(f"✅ Script de migration créé: {script_file}")
    return True

def show_migration_summary(old_config):
    """Affiche un résumé de la migration"""
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DE LA MIGRATION")
    print("-" * 40)
    
    if old_config:
        print("🔍 Configuration extraite:")
        for key, value in old_config.items():
            print(f"   {key}: {value}")
    
    print("\n🔄 Changements principaux:")
    print("   ❌ FOLD = 'all' → ✅ Validation croisée (5 folds)")
    print("   ❌ Configuration hardcodée → ✅ Configuration flexible")
    print("   ❌ Pas de gestion d'erreurs → ✅ Logging robuste")
    print("   ❌ Pas de monitoring → ✅ Analyse automatique")
    
    print("\n🚀 Prochaines étapes:")
    print("   1. Vérifiez migrated_config.py")
    print("   2. Adaptez les chemins si nécessaire")
    print("   3. Testez avec: python run_migrated.py")
    print("   4. Ou utilisez: python train_nnunet.py --preset debug")
    
    print("\n💡 Avantages de la nouvelle version:")
    print("   ✅ Validation croisée robuste (5 folds)")
    print("   ✅ Métriques fiables (moyenne ± écart-type)")
    print("   ✅ Configuration flexible (presets, arguments)")
    print("   ✅ Gestion d'erreurs et logging")
    print("   ✅ Analyse automatique des résultats")
    print("   ✅ Parallélisation possible (multi-GPU)")

def main():
    """Fonction principale de migration"""
    print("🔄 Migration depuis l'ancien train_nnunet.py")
    print("=" * 50)
    
    # 1. Sauvegarder l'ancien script
    has_old_script = backup_old_script()
    
    if not has_old_script:
        print("\nℹ️  Aucune migration nécessaire.")
        print("Utilisez directement le nouveau système:")
        print("   python train_nnunet.py --preset debug")
        return True
    
    # 2. Extraire la configuration
    print("\n🔍 Extraction de la configuration...")
    old_config = extract_old_config()
    
    if old_config:
        print("✅ Configuration extraite avec succès")
    else:
        print("⚠️  Impossible d'extraire la configuration complète")
        old_config = {}
    
    # 3. Créer les fichiers de migration
    print("\n📝 Création des fichiers de migration...")
    create_migrated_config(old_config)
    create_migration_script(old_config)
    
    # 4. Résumé
    show_migration_summary(old_config)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
